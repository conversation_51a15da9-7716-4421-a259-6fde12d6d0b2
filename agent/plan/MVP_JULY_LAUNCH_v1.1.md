# MVP July Launch v1.1 - Product Requirements Document

## Background and Motivation

Following a strategic review to meet a two-week deadline, we are shifting to a **hyper-focused MVP approach**. This revised plan prioritizes the most critical, high-value features that deliver a complete and demonstrable user journey. The core strengths of the check-in system, theme extraction, and conversational interaction remain the foundation.

**Strategic Focus**: Create a workable, polished, and downloadable app that one close friend can start using, establishing the foundation for user feedback and iterative development.


## MVP SCOPE DEFINITION

### **MVP Features (Current Implementation Focus)**

1. **Phase 1: CHECK-IN [MVP]** ✅
   - Opening stage: Voice journaling and theme extraction
   - Transition stage: Theme presentation and phase selection
   - Fully implemented with DialogueChain and TransitionChain

2. **Theme Visualization System [MVP]**
   - Display extracted themes in organized format
   - View themes from previous sessions
   - Success Criteria: User can review and understand conversation themes

3. **Conversation Replay & Summarization [MVP]**
   - "Repeat last response" functionality
   - "Summarize themes" voice command
   - Success Criteria: User can request spoken summary of themes

## Path to Immediate Release (Check-In Focus)

This section outlines the final tasks required to deliver a polished, stable release focused exclusively on the **Check-In and Theme Review** experience.

### 1. Define Conversation End Point
- **Task**: Explicitly define the end of the Check-In flow. After presenting the themes, the agent should provide a summary and gracefully conclude the conversation, returning to an idle state.
- **Example Script**: "I've analyzed our conversation and identified these key themes... This gives us a great foundation to work from. Let me know when you're ready to check in again."
- **Goal**: Create a clear and satisfying conclusion to the core loop, leaving the user with a sense of completion.

### 2. Stabilize the Core Loop
- **Task**: Temporarily disable the transition to `WISH_COLLECTION` in `TransitionChain.kt`. The agent should guide the user through the check-in and theme review, but not attempt to start the new conversational wish creation flow.
- **Goal**: Ensure the app provides a complete, end-to-end experience without hitting the known bug in the `ConversationAgent`'s handling of the wish creation loop.

### 3. Verify `CommandMode` Wish Management
- **Task**: Confirm that the existing `CommandMode` for wish management (add, edit, delete wishes via voice commands) is functional and polished.
- **Items**:
    - Test the voice commands for selecting, adding, and changing wishes.
    - Ensure the UI feedback is clear.
- **Goal**: Retain the app's core goal-management utility, providing a valuable, stable feature alongside the Check-In process.

### 4. Final UI/UX Polish
- **Task**: Perform a final review of the main screen and conversation interface.
- **Items**:
    - Ensure text is readable and well-formatted.
    - Check for any visual glitches or alignment issues.
    - Verify that the microphone state and agent status are always clear to the user.
- **Goal**: Create a clean, professional, and intuitive user interface.

### 5. Add Simple Onboarding Guidance
- **Task**: On the first app launch, have the agent provide a brief, one-time spoken introduction.
- **Example Script**: "Welcome to Vox Manifestor. To begin, just press the microphone button and tell me what's on your mind. When you're done, press it again, and I'll help you find the themes in your thoughts."
- **Goal**: Ensure a new user understands how to start their first session without confusion.

### 6. Robust Error Handling
- **Task**: Test and confirm graceful handling of common errors.
- **Scenarios**:
    - No network connection.
    - LLM API errors or timeouts.
    - Microphone permission denied.
- **Goal**: The app should recover gracefully and provide clear feedback to the user when an error occurs, rather than crashing or freezing.


### **POST-MVP Features (Deferred)**

1. **Phase 2: WISH_COLLECTION [POST-MVP]** 🔄
   - WishCreator implementation (partially complete)
   - Conversational wish creation and refinement
   - Database integration via WishUtilities

2. **Phase 3: PRESENT_STATE_EXPLORATION [POST-MVP]**
   - Explore current reality for selected wish
   - Detailed questioning about present situation

3. **Phase 4: DESIRED_STATE_EXPLORATION [POST-MVP]**
   - Define desired outcomes and future states
   - Visualization and detailed description

4. **Phase 5: CONTRAST_ANALYSIS [POST-MVP]**
   - Analyze gaps between present and desired states
   - Identify obstacles and challenges

5. **Phase 6: AFFIRMATION_PROCESS [POST-MVP]**
   - Reinforce commitment and positive visualization
   - Belief-change and reframing processes

6. **Phase 7: LOOP_DECISION [POST-MVP]**
   - Determine next action or end session
   - Session management and continuation logic

---

## Deferred to Post-MVP

The following features from the original v1.0 plan have been intentionally deferred to a post-MVP release to ensure a focused and timely launch.

---

0.  **Conversational Goal Management**
    -   **Task**: Implement an LLM-driven goal definition process, enabling the creation, editing, and removal of the 5 main life goals (wishes) entirely through dialogue.
    -   **Reason for Deferral**: The initial implementation revealed integration complexities with the `ConversationAgent`'s core loop. To ensure immediate stability for the first user, we will temporarily disable this feature and rely on the existing `CommandMode` for wish management. This allows for a polished release of the core Check-In feature without delaying for bug-fixing on a non-critical path.
    -   **Success Criteria**: User can fully manage their 5 core wishes entirely through conversation.

1.  **ConversationAgent Architectural Completion**
    -   **Task**: Complete the final modularization of `ConversationAgent` by extracting all concept-building logic into a new, dedicated `ConceptManager`.
    -   **Technical Benefits**: Reduces the size and complexity of `ConversationAgent`, completing the 6-module refactoring effort and improving long-term maintainability.
    -   **Reason for Deferral**: While architecturally beneficial, this is a non-user-facing refactor. The existing integration is functional for the MVP scope. Deferring this allows us to focus all effort on delivering user-facing features.
    -   **Success Criteria**: `ConversationAgent` is a pure orchestrator, and all concept-specific logic is isolated in `ConceptManager`, following the "Incremental Extraction" model.

2.  **Main Screen UI Refinement - High-Tech Interface Upgrade**
    -   **Task**: Overhaul the main screen UI with a more sophisticated, "Star Trek-style" aesthetic, including unified goal lists, geometric dividers, and transparency effects.
    -   **Reason for Deferral**: Cosmetic enhancement. The current UI is functional for the MVP.

3.  **Conversation History Enhancement**
    -   **Task**: Create a dynamic, animated conversation history panel with a toggle between current and previous sessions.
    -   **Reason for Deferral**: A "nice-to-have" feature. Basic history is sufficient for the MVP.

4.  **Main Screen Goal Display Enhancement**
    -   **Task**: Add extra details to the goal display on the main screen, such as last-updated timestamps.
    -   **Reason for Deferral**: Minor enhancement.

5.  **Check-in Session Control**
    -   **Task**: Add a "Start New Check-in" button and implement logic to choose between continuing a session or starting fresh.
    -   **Reason for Deferral**: The default behavior (always continuing the last session) is sufficient for the initial MVP.

6.  **Session Resume Enhancement**
    -   **Task**: Improve the context preservation and theme visibility when resuming conversations across app sessions.
    -   **Reason for Deferral**: The current session handling is adequate for initial testing.

7.  **User Experience Refinements & Launch Readiness**
    -   **Task**: Implement a simple onboarding flow, add helpful voice command hints, and add more robust error handling.
    -   **Reason for Deferral**: These are polish items best addressed after the core functionality is validated with a real user.

